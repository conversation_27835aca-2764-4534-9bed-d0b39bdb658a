一、HR资格面试

20分钟左右，面试的是华为苏州那边的关于智能驾驶测试，问了一些自身基本情况，还提了一下有没有驾照，稍微问了一点软件测试的知识。

二、业务面试一

自我介绍，然后做了一个简单代码题：写一个时间复杂度O(N*logN)的排序算法，对数组进行排序，我写了一个计数排序。然后问了一下测试项目里遇到的难题以及解决方法。

三、业务面试二

自我介绍，然后说了一下项目，做了一个手撕：返回字符串中第一个重复出现的字符，用哈希表遍历一下就行。

四、业务面试三

前两次业务面试定级不一样，上来问了为什么简历里面写的大部分是开发，为什么要来面测试，对测试的理解是什么，问了黑白盒，基本数据结构，java里的AaaryList，以及java里的关键字，Java对ArrayList排序的方法或类，写了个手撕：

请设计一个租房信息管理系统，完成核心类RoomManager，需要实现如下方法。
    public boolean addRoom(int id, int area, int price, int rooms)：在系统中增加一套编号为 id，面积为 area，月租金为 price，卧室数量为 rooms的房源：
      1）若系统中不存在编号为 id的房源，则添加该房源，返回 true；
      2）若已存在，则将对应房源信息更新为新传入的 area、price、rooms，并返回 false；
    public boolean deleteRoom(int id)：删除系统中指定id的房源：
     若存在符合条件的房源，删除该房源并返回 true；若不存在，返回 false。
    public int[] queryRoom(int area, int price, int rooms)：查询符合筛选条件的房源id。返回结果按价格升序排列。
   Ø  筛选条件：面积大于等于 area，月租金小于等于 price，卧室数为 rooms 的房源；